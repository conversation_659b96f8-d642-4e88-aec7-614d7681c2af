# 更新日志

## v1.0.2 (2024-06-11)

### ✨ 新增功能
- **动态模型选择**: 支持通过API获取可用模型列表或手动输入自定义模型版本
- **多平台模型管理**: 支持OpenAI、Anthropic、Google等多个LLM平台
- **自定义模型版本**: 支持输入如`gemini-2.0-flash-exp`等自定义模型版本
- **独立API Key管理**: 每个平台的API Key独立存储和显示
- **API Key显示控制**: 添加眼睛图标切换API Key显示/隐藏状态

### 🔧 修复
- **平台切换API Key保持**: 修复平台切换时API Key显示错误平台密钥的问题
- **API Key安全显示**: 默认隐藏API Key，点击眼睛图标可切换显示状态
- **模型信息展示**: 显示详细的模型信息包括API端点、最大Token等

### 🎨 界面改进
- **API Key输入框**: 添加显示/隐藏切换按钮，提升安全性和用户体验
- **平台选择界面**: 优化LLM配置界面，支持平台切换和模型选择
- **模型信息卡片**: 显示选中模型的详细信息和推荐配置

### 📝 技术改进
- **模型管理器**: 新增ModelManager模块，统一管理所有LLM平台和模型
- **配置结构优化**: 支持多平台API Key独立存储
- **缓存机制**: 模型列表缓存，减少API调用次数

## v1.0.1 (2024-06-11)

### 🔧 修复
- **Gemini模型更新**: 将Gemini Pro Vision更新为Gemini 1.5 Flash，解决模型弃用问题
- **404错误修复**: 修复测试页面中的资源加载问题
- **代码优化**: 清理未使用的导入和变量

### ✨ 新增功能
- **增强的LLM配置**: 添加最大Token数和自定义提示词配置
- **模型选择测试**: 在测试页面添加LLM模型配置测试功能
- **更详细的配置界面**: 显示具体的模型名称和版本信息

### 🎨 界面改进
- **表单元素支持**: 添加对textarea和number输入框的样式支持
- **配置提示**: 在模型选择中显示具体的模型版本信息
- **测试功能增强**: 添加模型特定的配置测试

### 📝 文档更新
- **使用指南更新**: 更新Gemini模型相关信息
- **API文档**: 添加新配置字段的说明

## v1.0.0 (2024-06-11)

### 🎉 初始版本
- **多OCR服务支持**: 百度OCR、腾讯云OCR、LLM视觉模型
- **多种输入方式**: 屏幕截图、文件选择、拖拽支持
- **智能配置管理**: 配置验证、测试连接、安全存储
- **现代化UI**: 响应式设计、实时通知、键盘快捷键
- **模块化架构**: 清晰的代码结构，易于维护和扩展

### 支持的服务
- **百度OCR**: 通用文字识别、高精度文字识别
- **腾讯云OCR**: 通用印刷体识别
- **OpenAI GPT-4V**: 视觉理解和文字识别
- **Claude Vision**: 高准确率的视觉识别
- **Gemini Vision**: 快速响应的视觉模型

### 核心功能
- 屏幕截图OCR识别
- 本地图片文件识别
- 拖拽图片识别
- uTools图片匹配
- 多服务配置切换
- 配置导入导出
- 连接测试验证
- 实时结果显示
- 一键复制结果

---

## 开发说明

### 版本号规则
- 主版本号：重大功能更新或架构变更
- 次版本号：新功能添加
- 修订版本号：Bug修复和小改进

### 更新类型
- 🎉 **新功能**: 全新的功能特性
- 🔧 **修复**: Bug修复和问题解决
- 🎨 **界面**: UI/UX改进和视觉优化
- 📝 **文档**: 文档更新和说明改进
- ⚡ **性能**: 性能优化和速度提升
- 🔒 **安全**: 安全相关的修复和改进
