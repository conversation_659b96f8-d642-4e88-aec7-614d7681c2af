# 更新日志

## v1.0.1 (2024-06-11)

### 🔧 修复
- **Gemini模型更新**: 将Gemini Pro Vision更新为Gemini 1.5 Flash，解决模型弃用问题
- **404错误修复**: 修复测试页面中的资源加载问题
- **代码优化**: 清理未使用的导入和变量

### ✨ 新增功能
- **增强的LLM配置**: 添加最大Token数和自定义提示词配置
- **模型选择测试**: 在测试页面添加LLM模型配置测试功能
- **更详细的配置界面**: 显示具体的模型名称和版本信息

### 🎨 界面改进
- **表单元素支持**: 添加对textarea和number输入框的样式支持
- **配置提示**: 在模型选择中显示具体的模型版本信息
- **测试功能增强**: 添加模型特定的配置测试

### 📝 文档更新
- **使用指南更新**: 更新Gemini模型相关信息
- **API文档**: 添加新配置字段的说明

## v1.0.0 (2024-06-11)

### 🎉 初始版本
- **多OCR服务支持**: 百度OCR、腾讯云OCR、LLM视觉模型
- **多种输入方式**: 屏幕截图、文件选择、拖拽支持
- **智能配置管理**: 配置验证、测试连接、安全存储
- **现代化UI**: 响应式设计、实时通知、键盘快捷键
- **模块化架构**: 清晰的代码结构，易于维护和扩展

### 支持的服务
- **百度OCR**: 通用文字识别、高精度文字识别
- **腾讯云OCR**: 通用印刷体识别
- **OpenAI GPT-4V**: 视觉理解和文字识别
- **Claude Vision**: 高准确率的视觉识别
- **Gemini Vision**: 快速响应的视觉模型

### 核心功能
- 屏幕截图OCR识别
- 本地图片文件识别
- 拖拽图片识别
- uTools图片匹配
- 多服务配置切换
- 配置导入导出
- 连接测试验证
- 实时结果显示
- 一键复制结果

---

## 开发说明

### 版本号规则
- 主版本号：重大功能更新或架构变更
- 次版本号：新功能添加
- 修订版本号：Bug修复和小改进

### 更新类型
- 🎉 **新功能**: 全新的功能特性
- 🔧 **修复**: Bug修复和问题解决
- 🎨 **界面**: UI/UX改进和视觉优化
- 📝 **文档**: 文档更新和说明改进
- ⚡ **性能**: 性能优化和速度提升
- 🔒 **安全**: 安全相关的修复和改进
