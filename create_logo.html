<!DOCTYPE html>
<html>
<head>
    <title>Create Logo</title>
</head>
<body>
    <canvas id="logoCanvas" width="128" height="128"></canvas>
    <script>
        const canvas = document.getElementById('logoCanvas');
        const ctx = canvas.getContext('2d');
        
        // 背景渐变
        const gradient = ctx.createLinearGradient(0, 0, 128, 128);
        gradient.addColorStop(0, '#4facfe');
        gradient.addColorStop(1, '#00f2fe');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 128, 128);
        
        // 圆角
        ctx.globalCompositeOperation = 'destination-in';
        ctx.beginPath();
        ctx.roundRect(0, 0, 128, 128, 20);
        ctx.fill();
        
        ctx.globalCompositeOperation = 'source-over';
        
        // 文档图标
        ctx.fillStyle = 'white';
        ctx.fillRect(25, 20, 60, 80);
        
        // 文档折角
        ctx.fillStyle = '#e0e0e0';
        ctx.beginPath();
        ctx.moveTo(75, 20);
        ctx.lineTo(85, 30);
        ctx.lineTo(75, 30);
        ctx.closePath();
        ctx.fill();
        
        // 文字线条
        ctx.fillStyle = '#333';
        ctx.fillRect(35, 35, 35, 3);
        ctx.fillRect(35, 45, 40, 3);
        ctx.fillRect(35, 55, 30, 3);
        ctx.fillRect(35, 65, 35, 3);
        
        // 扫描线效果
        ctx.strokeStyle = '#ff6b6b';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(15, 75);
        ctx.lineTo(95, 75);
        ctx.stroke();
        
        // 下载图片
        const link = document.createElement('a');
        link.download = 'logo.png';
        link.href = canvas.toDataURL();
        link.click();
    </script>
</body>
</html>
