@echo off
echo ================================
echo uTools OCR插件部署脚本
echo ================================

echo.
echo 正在检查文件结构...

if not exist "plugin.json" (
    echo 错误: 找不到plugin.json文件
    pause
    exit /b 1
)

if not exist "index.html" (
    echo 错误: 找不到index.html文件
    pause
    exit /b 1
)

if not exist "preload.js" (
    echo 错误: 找不到preload.js文件
    pause
    exit /b 1
)

if not exist "assets\logo.png" (
    echo 警告: 找不到logo.png文件
)

if not exist "src\main.js" (
    echo 错误: 找不到src\main.js文件
    pause
    exit /b 1
)

echo ✓ 文件结构检查完成

echo.
echo 正在创建部署包...

set DEPLOY_DIR=OCR_Plugin_Deploy
if exist "%DEPLOY_DIR%" rmdir /s /q "%DEPLOY_DIR%"
mkdir "%DEPLOY_DIR%"

echo 复制核心文件...
copy "plugin.json" "%DEPLOY_DIR%\"
copy "index.html" "%DEPLOY_DIR%\"
copy "preload.js" "%DEPLOY_DIR%\"
copy "README.md" "%DEPLOY_DIR%\"

echo 复制资源文件...
mkdir "%DEPLOY_DIR%\assets"
copy "assets\*" "%DEPLOY_DIR%\assets\"

echo 复制源代码...
mkdir "%DEPLOY_DIR%\src"
copy "src\*" "%DEPLOY_DIR%\src\"

echo.
echo ================================
echo 部署包创建完成！
echo 位置: %CD%\%DEPLOY_DIR%
echo ================================
echo.
echo 安装说明:
echo 1. 打开uTools
echo 2. 进入插件管理
echo 3. 点击"安装本地插件"
echo 4. 选择 %DEPLOY_DIR% 文件夹
echo 5. 完成安装
echo.
echo 使用说明:
echo 1. 在uTools中输入"OCR配置"进行配置
echo 2. 配置完成后输入"OCR"开始使用
echo.

pause
