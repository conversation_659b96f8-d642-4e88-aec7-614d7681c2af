// OCR插件主逻辑
class OCRPlugin {
    constructor() {
        this.configManager = new ConfigManager();
        this.ocrServices = new OCRServices();
        this.uiManager = new UIManager();
        this.config = this.configManager.getConfig();
        this.init();
    }

    init() {
        this.uiManager.init();
        this.bindEvents();
        this.loadConfigUI();

        // 根据启动参数决定初始行为
        const feature = this.getCurrentFeature();
        if (feature === 'ocr-config') {
            this.uiManager.showConfigView();
        } else if (feature === 'ocr-image') {
            // 如果是图片匹配进入，直接处理图片
            this.handleImageInput();
        }
    }

    getCurrentFeature() {
        return window.ocrAPI?.getCurrentFeature?.() || null;
    }

    bindEvents() {
        // 主界面事件
        document.getElementById('screenshot-btn').addEventListener('click', () => {
            this.takeScreenshot();
        });

        document.getElementById('upload-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });

        document.getElementById('config-btn').addEventListener('click', () => {
            this.uiManager.showConfigView();
        });

        document.getElementById('copy-btn').addEventListener('click', () => {
            this.copyResult();
        });

        document.getElementById('clear-btn').addEventListener('click', () => {
            this.uiManager.clearResult();
        });

        // 配置界面事件
        document.getElementById('back-btn').addEventListener('click', () => {
            this.uiManager.showMainView();
        });

        document.getElementById('ocr-service').addEventListener('change', (e) => {
            this.uiManager.switchConfigSection(e.target.value);
        });

        document.getElementById('save-config-btn').addEventListener('click', () => {
            this.saveConfig();
        });

        document.getElementById('test-config-btn').addEventListener('click', () => {
            this.testConfig();
        });
    }

    // 截图功能
    takeScreenshot() {
        this.uiManager.showLoading('正在截图...');

        window.ocrAPI.screenCapture((imageBase64) => {
            if (imageBase64) {
                this.performOCR(imageBase64);
            } else {
                this.uiManager.hideLoading();
                this.uiManager.showError('截图失败');
            }
        });
    }

    // 处理文件选择
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.uiManager.showError('请选择图片文件');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.performOCR(e.target.result);
        };
        reader.readAsDataURL(file);
    }

    // 处理图片输入（来自uTools的图片匹配）
    handleImageInput() {
        // 这里需要从uTools获取传入的图片数据
        // 具体实现取决于uTools如何传递图片数据
        console.log('处理图片输入');
    }

    // 执行OCR识别
    async performOCR(imageBase64) {
        const validation = this.configManager.validateConfig(this.config);
        if (!validation.valid) {
            this.uiManager.showError(validation.message || '请先配置OCR服务');
            return;
        }

        this.uiManager.showLoading('正在识别文字...');

        try {
            const serviceConfig = this.configManager.getServiceConfig(this.config, this.config.service);
            const result = await this.ocrServices.performOCR(
                imageBase64,
                this.config.service,
                serviceConfig
            );

            this.uiManager.hideLoading();

            if (result.success) {
                this.uiManager.showResult(result.text, result.confidence, result.details);
            } else {
                this.uiManager.showError(result.error || '识别失败');
            }
        } catch (error) {
            this.uiManager.hideLoading();
            this.uiManager.showError('识别过程中发生错误: ' + error.message);
        }
    }

    // 复制结果
    copyResult() {
        const resultText = document.getElementById('result-text').textContent;
        if (resultText) {
            window.ocrAPI.copyText(resultText);
            this.uiManager.showNotification('已复制到剪贴板');
        }
    }





    // 加载配置到UI
    loadConfigUI() {
        // 设置服务选择
        document.getElementById('ocr-service').value = this.config.service;
        this.uiManager.switchConfigSection(this.config.service);

        // 百度配置
        document.getElementById('baidu-api-key').value = this.config.baidu?.apiKey || '';
        document.getElementById('baidu-secret-key').value = this.config.baidu?.secretKey || '';
        document.getElementById('baidu-type').value = this.config.baidu?.type || 'general_basic';

        // 腾讯云配置
        document.getElementById('tencent-secret-id').value = this.config.tencent?.secretId || '';
        document.getElementById('tencent-secret-key').value = this.config.tencent?.secretKey || '';

        // 阿里云配置
        document.getElementById('aliyun-access-key').value = this.config.aliyun?.accessKey || '';
        document.getElementById('aliyun-access-secret').value = this.config.aliyun?.accessSecret || '';

        // LLM配置
        document.getElementById('llm-model').value = this.config.llm?.model || 'openai-gpt4v';
        document.getElementById('llm-api-key').value = this.config.llm?.apiKey || '';
        document.getElementById('llm-base-url').value = this.config.llm?.baseUrl || '';
        document.getElementById('llm-max-tokens').value = this.config.llm?.maxTokens || 1000;
        document.getElementById('llm-prompt').value = this.config.llm?.prompt || '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。';
    }

    // 保存配置
    saveConfig() {
        const newConfig = {
            service: document.getElementById('ocr-service').value,
            baidu: {
                apiKey: document.getElementById('baidu-api-key').value,
                secretKey: document.getElementById('baidu-secret-key').value,
                type: document.getElementById('baidu-type').value
            },
            tencent: {
                secretId: document.getElementById('tencent-secret-id').value,
                secretKey: document.getElementById('tencent-secret-key').value
            },
            aliyun: {
                accessKey: document.getElementById('aliyun-access-key').value,
                accessSecret: document.getElementById('aliyun-access-secret').value
            },
            llm: {
                model: document.getElementById('llm-model').value,
                apiKey: document.getElementById('llm-api-key').value,
                baseUrl: document.getElementById('llm-base-url').value,
                maxTokens: parseInt(document.getElementById('llm-max-tokens').value) || 1000,
                prompt: document.getElementById('llm-prompt').value
            }
        };

        const result = this.configManager.saveConfig(newConfig);
        if (result.success) {
            this.config = result.config;
            this.uiManager.showNotification('配置保存成功');
        } else {
            this.uiManager.showError('配置保存失败: ' + result.error);
        }
    }

    // 测试配置
    async testConfig() {
        const validation = this.configManager.validateConfig(this.config);
        if (!validation.valid) {
            this.uiManager.showError(validation.message || '请先完善配置信息');
            return;
        }

        this.uiManager.showNotification('正在测试连接...', 'info');

        // 创建一个简单的测试图片（白底黑字"TEST"）
        const canvas = document.createElement('canvas');
        canvas.width = 200;
        canvas.height = 100;
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 200, 100);
        ctx.fillStyle = 'black';
        ctx.font = '30px Arial';
        ctx.fillText('TEST', 70, 60);

        const testImage = canvas.toDataURL();

        try {
            const serviceConfig = this.configManager.getServiceConfig(this.config, this.config.service);
            const result = await this.ocrServices.performOCR(
                testImage,
                this.config.service,
                serviceConfig
            );

            if (result.success) {
                this.uiManager.showNotification('连接测试成功');
            } else {
                this.uiManager.showError('连接测试失败: ' + result.error);
            }
        } catch (error) {
            this.uiManager.showError('连接测试失败: ' + error.message);
        }
    }


}

// 初始化插件
document.addEventListener('DOMContentLoaded', () => {
    // 确保所有模块都已加载
    if (typeof ConfigManager !== 'undefined' &&
        typeof OCRServices !== 'undefined' &&
        typeof UIManager !== 'undefined') {
        window.ocrPlugin = new OCRPlugin();
        window.configManager = window.ocrPlugin.configManager;
    } else {
        console.error('OCR插件模块加载失败');
    }
});
