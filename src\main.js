// OCR插件主逻辑
class OCRPlugin {
    constructor() {
        this.configManager = new ConfigManager();
        this.ocrServices = new OCRServices();
        this.uiManager = new UIManager();
        this.modelManager = new ModelManager();
        this.config = this.configManager.getConfig();
        this.init();
    }

    init() {
        this.uiManager.init();
        this.bindEvents();
        this.loadConfigUI();

        // 根据启动参数决定初始行为
        const feature = this.getCurrentFeature();
        if (feature === 'ocr-config') {
            this.uiManager.showConfigView();
        } else if (feature === 'ocr-image') {
            // 如果是图片匹配进入，直接处理图片
            this.handleImageInput();
        }
    }

    getCurrentFeature() {
        return window.ocrAPI?.getCurrentFeature?.() || null;
    }

    bindEvents() {
        // 主界面事件
        document.getElementById('screenshot-btn').addEventListener('click', () => {
            this.takeScreenshot();
        });

        document.getElementById('upload-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });

        document.getElementById('config-btn').addEventListener('click', () => {
            this.uiManager.showConfigView();
        });

        document.getElementById('copy-btn').addEventListener('click', () => {
            this.copyResult();
        });

        document.getElementById('clear-btn').addEventListener('click', () => {
            this.uiManager.clearResult();
        });

        // 配置界面事件
        document.getElementById('back-btn').addEventListener('click', () => {
            this.uiManager.showMainView();
        });

        document.getElementById('ocr-service').addEventListener('change', (e) => {
            this.uiManager.switchConfigSection(e.target.value);
        });

        // LLM平台切换事件
        document.getElementById('llm-platform').addEventListener('change', (e) => {
            this.handlePlatformChange(e.target.value);
        });

        // 刷新模型列表事件
        document.getElementById('refresh-models-btn').addEventListener('click', () => {
            this.refreshModelList();
        });

        // 自定义模型切换事件
        document.getElementById('use-custom-model').addEventListener('change', (e) => {
            this.toggleCustomModel(e.target.checked);
        });

        // 模型选择变化事件
        document.getElementById('llm-model-select').addEventListener('change', (e) => {
            this.handleModelChange(e.target.value);
        });

        // API Key显示/隐藏切换事件
        document.getElementById('toggle-api-key').addEventListener('click', () => {
            this.toggleApiKeyVisibility();
        });

        // API Key输入变化事件（保存到对应平台）
        document.getElementById('llm-api-key').addEventListener('input', (e) => {
            this.handleApiKeyChange(e.target.value);
        });

        document.getElementById('save-config-btn').addEventListener('click', () => {
            this.saveConfig();
        });

        document.getElementById('test-config-btn').addEventListener('click', () => {
            this.testConfig();
        });
    }

    // 截图功能
    takeScreenshot() {
        this.uiManager.showLoading('正在截图...');

        window.ocrAPI.screenCapture((imageBase64) => {
            if (imageBase64) {
                this.performOCR(imageBase64);
            } else {
                this.uiManager.hideLoading();
                this.uiManager.showError('截图失败');
            }
        });
    }

    // 处理文件选择
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.uiManager.showError('请选择图片文件');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.performOCR(e.target.result);
        };
        reader.readAsDataURL(file);
    }

    // 处理图片输入（来自uTools的图片匹配）
    handleImageInput() {
        // 这里需要从uTools获取传入的图片数据
        // 具体实现取决于uTools如何传递图片数据
        console.log('处理图片输入');
    }

    // 执行OCR识别
    async performOCR(imageBase64) {
        const validation = this.configManager.validateConfig(this.config);
        if (!validation.valid) {
            this.uiManager.showError(validation.message || '请先配置OCR服务');
            return;
        }

        this.uiManager.showLoading('正在识别文字...');

        try {
            const serviceConfig = this.configManager.getServiceConfig(this.config, this.config.service);
            const result = await this.ocrServices.performOCR(
                imageBase64,
                this.config.service,
                serviceConfig
            );

            this.uiManager.hideLoading();

            if (result.success) {
                this.uiManager.showResult(result.text, result.confidence, result.details);
            } else {
                this.uiManager.showError(result.error || '识别失败');
            }
        } catch (error) {
            this.uiManager.hideLoading();
            this.uiManager.showError('识别过程中发生错误: ' + error.message);
        }
    }

    // 复制结果
    copyResult() {
        const resultText = document.getElementById('result-text').textContent;
        if (resultText) {
            window.ocrAPI.copyText(resultText);
            this.uiManager.showNotification('已复制到剪贴板');
        }
    }





    // 加载配置到UI
    loadConfigUI() {
        // 设置服务选择
        document.getElementById('ocr-service').value = this.config.service;
        this.uiManager.switchConfigSection(this.config.service);

        // 百度配置
        document.getElementById('baidu-api-key').value = this.config.baidu?.apiKey || '';
        document.getElementById('baidu-secret-key').value = this.config.baidu?.secretKey || '';
        document.getElementById('baidu-type').value = this.config.baidu?.type || 'general_basic';

        // 腾讯云配置
        document.getElementById('tencent-secret-id').value = this.config.tencent?.secretId || '';
        document.getElementById('tencent-secret-key').value = this.config.tencent?.secretKey || '';

        // 阿里云配置
        document.getElementById('aliyun-access-key').value = this.config.aliyun?.accessKey || '';
        document.getElementById('aliyun-access-secret').value = this.config.aliyun?.accessSecret || '';

        // LLM配置
        const currentPlatform = this.config.llm?.platform || 'openai';
        document.getElementById('llm-platform').value = currentPlatform;

        // 根据当前平台设置API Key
        const currentApiKey = this.config.llm?.apiKeys?.[currentPlatform] || this.config.llm?.apiKey || '';
        document.getElementById('llm-api-key').value = currentApiKey;

        document.getElementById('llm-base-url').value = this.config.llm?.baseUrl || '';
        document.getElementById('llm-max-tokens').value = this.config.llm?.maxTokens || 1000;
        document.getElementById('llm-prompt').value = this.config.llm?.prompt || '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。';
        document.getElementById('use-custom-model').checked = this.config.llm?.useCustomModel || false;
        document.getElementById('custom-model-name').value = this.config.llm?.customModel || '';

        // 初始化API Key显示状态
        this.initApiKeyToggle();

        // 初始化平台相关配置
        this.handlePlatformChange(currentPlatform);
        this.toggleCustomModel(this.config.llm?.useCustomModel || false);
    }

    // 保存配置
    saveConfig() {
        const newConfig = {
            service: document.getElementById('ocr-service').value,
            baidu: {
                apiKey: document.getElementById('baidu-api-key').value,
                secretKey: document.getElementById('baidu-secret-key').value,
                type: document.getElementById('baidu-type').value
            },
            tencent: {
                secretId: document.getElementById('tencent-secret-id').value,
                secretKey: document.getElementById('tencent-secret-key').value
            },
            aliyun: {
                accessKey: document.getElementById('aliyun-access-key').value,
                accessSecret: document.getElementById('aliyun-access-secret').value
            },
            llm: {
                platform: document.getElementById('llm-platform').value,
                model: document.getElementById('llm-model-select').value,
                useCustomModel: document.getElementById('use-custom-model').checked,
                customModel: document.getElementById('custom-model-name').value,
                // 保存所有平台的API Key
                apiKeys: this.getCurrentApiKeys(),
                baseUrl: document.getElementById('llm-base-url').value,
                maxTokens: parseInt(document.getElementById('llm-max-tokens').value) || 1000,
                prompt: document.getElementById('llm-prompt').value
            }
        };

        const result = this.configManager.saveConfig(newConfig);
        if (result.success) {
            this.config = result.config;
            this.uiManager.showNotification('配置保存成功');
        } else {
            this.uiManager.showError('配置保存失败: ' + result.error);
        }
    }

    // 测试配置
    async testConfig() {
        const validation = this.configManager.validateConfig(this.config);
        if (!validation.valid) {
            this.uiManager.showError(validation.message || '请先完善配置信息');
            return;
        }

        this.uiManager.showNotification('正在测试连接...', 'info');

        // 创建一个简单的测试图片（白底黑字"TEST"）
        const canvas = document.createElement('canvas');
        canvas.width = 200;
        canvas.height = 100;
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 200, 100);
        ctx.fillStyle = 'black';
        ctx.font = '30px Arial';
        ctx.fillText('TEST', 70, 60);

        const testImage = canvas.toDataURL();

        try {
            const serviceConfig = this.configManager.getServiceConfig(this.config, this.config.service);
            const result = await this.ocrServices.performOCR(
                testImage,
                this.config.service,
                serviceConfig
            );

            if (result.success) {
                this.uiManager.showNotification('连接测试成功');
            } else {
                this.uiManager.showError('连接测试失败: ' + result.error);
            }
        } catch (error) {
            this.uiManager.showError('连接测试失败: ' + error.message);
        }
    }

    // 处理平台切换
    async handlePlatformChange(platform) {
        const modelSelect = document.getElementById('llm-model-select');
        const modelInfo = document.getElementById('model-info');
        const apiKeyInput = document.getElementById('llm-api-key');

        // 清空当前选项
        modelSelect.innerHTML = '<option value="">加载中...</option>';
        modelInfo.style.display = 'none';

        // 切换到对应平台的API Key
        this.switchPlatformApiKey(platform);

        try {
            const apiKey = apiKeyInput.value;
            const baseUrl = document.getElementById('llm-base-url').value;

            if (apiKey) {
                // 尝试获取模型列表
                const models = await this.modelManager.getModels(platform, apiKey, baseUrl);
                this.populateModelSelect(models);
            } else {
                // 使用默认模型列表
                const platformInfo = this.modelManager.getPlatformInfo(platform);
                if (platformInfo) {
                    const defaultModels = platformInfo.defaultModels.map(model => ({
                        id: model,
                        name: model,
                        description: this.modelManager.getModelDescription(model),
                        isDefault: true
                    }));
                    this.populateModelSelect(defaultModels);
                }
            }

            // 设置当前选中的模型
            if (this.config.llm?.platform === platform && this.config.llm?.model) {
                modelSelect.value = this.config.llm.model;
                this.handleModelChange(this.config.llm.model);
            }
        } catch (error) {
            console.error('获取模型列表失败:', error);
            modelSelect.innerHTML = '<option value="">获取模型列表失败</option>';
        }
    }

    // 填充模型选择下拉框
    populateModelSelect(models) {
        const modelSelect = document.getElementById('llm-model-select');
        modelSelect.innerHTML = '';

        if (models.length === 0) {
            modelSelect.innerHTML = '<option value="">暂无可用模型</option>';
            return;
        }

        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = `${model.name}${model.isDefault ? ' (默认)' : ''}`;
            option.title = model.description || '';
            modelSelect.appendChild(option);
        });
    }

    // 刷新模型列表
    async refreshModelList() {
        const platform = document.getElementById('llm-platform').value;
        const apiKey = document.getElementById('llm-api-key').value;

        if (!apiKey) {
            this.uiManager.showError('请先输入API Key');
            return;
        }

        const refreshBtn = document.getElementById('refresh-models-btn');
        refreshBtn.disabled = true;
        refreshBtn.textContent = '刷新中...';

        try {
            // 清除缓存
            this.modelManager.clearCache();

            // 重新获取模型列表
            await this.handlePlatformChange(platform);

            this.uiManager.showNotification('模型列表已刷新');
        } catch (error) {
            this.uiManager.showError('刷新失败: ' + error.message);
        } finally {
            refreshBtn.disabled = false;
            refreshBtn.textContent = '刷新';
        }
    }

    // 切换自定义模型
    toggleCustomModel(useCustom) {
        const customGroup = document.getElementById('custom-model-group');
        const modelSelect = document.getElementById('llm-model-select');

        if (useCustom) {
            customGroup.style.display = 'block';
            modelSelect.disabled = true;
        } else {
            customGroup.style.display = 'none';
            modelSelect.disabled = false;
        }
    }

    // 处理模型变化
    handleModelChange(modelId) {
        if (!modelId) return;

        const platform = document.getElementById('llm-platform').value;
        const modelInfo = this.modelManager.getModelInfo(platform, modelId);

        if (modelInfo) {
            this.displayModelInfo(modelInfo);
        }
    }

    // 显示模型信息
    displayModelInfo(modelInfo) {
        const modelInfoDiv = document.getElementById('model-info');
        const modelDetails = document.getElementById('model-details');

        modelDetails.innerHTML = `
            <div class="model-detail">
                <span class="label">平台:</span>
                <span class="value">${modelInfo.platform}</span>
            </div>
            <div class="model-detail">
                <span class="label">模型ID:</span>
                <span class="value">${modelInfo.modelId}</span>
            </div>
            <div class="model-detail">
                <span class="label">描述:</span>
                <span class="value">${modelInfo.description}</span>
            </div>
            <div class="model-detail">
                <span class="label">API端点:</span>
                <span class="value">${modelInfo.apiEndpoint}</span>
            </div>
            <div class="model-detail">
                <span class="label">最大Token:</span>
                <span class="value">${modelInfo.maxTokens}</span>
            </div>
        `;

        modelInfoDiv.style.display = 'block';

        // 自动设置推荐的最大Token数
        const maxTokensInput = document.getElementById('llm-max-tokens');
        if (parseInt(maxTokensInput.value) === 1000) { // 只在默认值时自动设置
            maxTokensInput.value = modelInfo.maxTokens;
        }
    }
}

// 初始化插件
document.addEventListener('DOMContentLoaded', () => {
    // 确保所有模块都已加载
    if (typeof ConfigManager !== 'undefined' &&
        typeof OCRServices !== 'undefined' &&
        typeof UIManager !== 'undefined' &&
        typeof ModelManager !== 'undefined') {
        window.ocrPlugin = new OCRPlugin();
        window.configManager = window.ocrPlugin.configManager;
        window.modelManager = window.ocrPlugin.modelManager;
    } else {
        console.error('OCR插件模块加载失败');
        console.log('已加载的模块:', {
            ConfigManager: typeof ConfigManager !== 'undefined',
            OCRServices: typeof OCRServices !== 'undefined',
            UIManager: typeof UIManager !== 'undefined',
            ModelManager: typeof ModelManager !== 'undefined'
        });
    }
});
