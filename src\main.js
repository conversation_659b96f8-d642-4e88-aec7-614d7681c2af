// OCR插件主逻辑
class OCRPlugin {
    constructor() {
        this.currentView = 'main';
        this.config = this.loadConfig();
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadConfigUI();
        
        // 根据启动参数决定初始行为
        const feature = this.getCurrentFeature();
        if (feature === 'ocr-config') {
            this.showConfigView();
        } else if (feature === 'ocr-image') {
            // 如果是图片匹配进入，直接处理图片
            this.handleImageInput();
        }
    }

    getCurrentFeature() {
        return window.ocrAPI?.getCurrentFeature?.() || null;
    }

    bindEvents() {
        // 主界面事件
        document.getElementById('screenshot-btn').addEventListener('click', () => {
            this.takeScreenshot();
        });

        document.getElementById('upload-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });

        document.getElementById('config-btn').addEventListener('click', () => {
            this.showConfigView();
        });

        document.getElementById('copy-btn').addEventListener('click', () => {
            this.copyResult();
        });

        document.getElementById('clear-btn').addEventListener('click', () => {
            this.clearResult();
        });

        // 配置界面事件
        document.getElementById('back-btn').addEventListener('click', () => {
            this.showMainView();
        });

        document.getElementById('ocr-service').addEventListener('change', (e) => {
            this.switchConfigSection(e.target.value);
        });

        document.getElementById('save-config-btn').addEventListener('click', () => {
            this.saveConfig();
        });

        document.getElementById('test-config-btn').addEventListener('click', () => {
            this.testConfig();
        });
    }

    // 截图功能
    takeScreenshot() {
        this.showLoading('正在截图...');
        
        window.ocrAPI.screenCapture((imageBase64) => {
            if (imageBase64) {
                this.performOCR(imageBase64);
            } else {
                this.hideLoading();
                this.showError('截图失败');
            }
        });
    }

    // 处理文件选择
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.showError('请选择图片文件');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.performOCR(e.target.result);
        };
        reader.readAsDataURL(file);
    }

    // 处理图片输入（来自uTools的图片匹配）
    handleImageInput() {
        // 这里需要从uTools获取传入的图片数据
        // 具体实现取决于uTools如何传递图片数据
        console.log('处理图片输入');
    }

    // 执行OCR识别
    async performOCR(imageBase64) {
        if (!this.validateConfig()) {
            this.showError('请先配置OCR服务');
            return;
        }

        this.showLoading('正在识别文字...');

        try {
            const result = await window.ocrAPI.performOCR(
                imageBase64,
                this.config.service,
                this.getServiceConfig()
            );

            this.hideLoading();

            if (result.success) {
                this.showResult(result.text);
            } else {
                this.showError(result.error || '识别失败');
            }
        } catch (error) {
            this.hideLoading();
            this.showError('识别过程中发生错误: ' + error.message);
        }
    }

    // 显示结果
    showResult(text) {
        const resultContainer = document.getElementById('result-container');
        const resultText = document.getElementById('result-text');
        
        resultText.textContent = text;
        resultContainer.style.display = 'block';
        
        // 滚动到结果区域
        resultContainer.scrollIntoView({ behavior: 'smooth' });
    }

    // 复制结果
    copyResult() {
        const resultText = document.getElementById('result-text').textContent;
        if (resultText) {
            window.ocrAPI.copyText(resultText);
            this.showMessage('已复制到剪贴板');
        }
    }

    // 清空结果
    clearResult() {
        document.getElementById('result-container').style.display = 'none';
        document.getElementById('result-text').textContent = '';
    }

    // 显示/隐藏加载状态
    showLoading(message = '正在处理...') {
        const loading = document.getElementById('loading');
        loading.querySelector('p').textContent = message;
        loading.style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    // 显示错误信息
    showError(message) {
        alert('错误: ' + message);
    }

    // 显示提示信息
    showMessage(message) {
        // 简单的提示实现，可以后续优化为更好的UI
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 1000;
            font-size: 14px;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }

    // 切换到配置视图
    showConfigView() {
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'block';
        this.currentView = 'config';
    }

    // 切换到主视图
    showMainView() {
        document.getElementById('config-view').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.currentView = 'main';
    }

    // 切换配置区域
    switchConfigSection(service) {
        // 隐藏所有配置区域
        const sections = ['baidu-config', 'tencent-config', 'aliyun-config', 'llm-config'];
        sections.forEach(id => {
            document.getElementById(id).style.display = 'none';
        });

        // 显示对应的配置区域
        const sectionMap = {
            'baidu': 'baidu-config',
            'tencent': 'tencent-config',
            'aliyun': 'aliyun-config',
            'llm': 'llm-config'
        };

        const targetSection = sectionMap[service];
        if (targetSection) {
            document.getElementById(targetSection).style.display = 'block';
        }
    }

    // 加载配置
    loadConfig() {
        const config = window.ocrAPI.db.get('ocr-config');
        return config || {
            service: 'baidu',
            baidu: {
                apiKey: '',
                secretKey: '',
                type: 'general_basic'
            },
            tencent: {
                secretId: '',
                secretKey: ''
            },
            aliyun: {
                accessKey: '',
                accessSecret: ''
            },
            llm: {
                model: 'openai-gpt4v',
                apiKey: '',
                baseUrl: ''
            }
        };
    }

    // 加载配置到UI
    loadConfigUI() {
        // 设置服务选择
        document.getElementById('ocr-service').value = this.config.service;
        this.switchConfigSection(this.config.service);

        // 百度配置
        document.getElementById('baidu-api-key').value = this.config.baidu?.apiKey || '';
        document.getElementById('baidu-secret-key').value = this.config.baidu?.secretKey || '';
        document.getElementById('baidu-type').value = this.config.baidu?.type || 'general_basic';

        // 腾讯云配置
        document.getElementById('tencent-secret-id').value = this.config.tencent?.secretId || '';
        document.getElementById('tencent-secret-key').value = this.config.tencent?.secretKey || '';

        // 阿里云配置
        document.getElementById('aliyun-access-key').value = this.config.aliyun?.accessKey || '';
        document.getElementById('aliyun-access-secret').value = this.config.aliyun?.accessSecret || '';

        // LLM配置
        document.getElementById('llm-model').value = this.config.llm?.model || 'openai-gpt4v';
        document.getElementById('llm-api-key').value = this.config.llm?.apiKey || '';
        document.getElementById('llm-base-url').value = this.config.llm?.baseUrl || '';
    }

    // 保存配置
    saveConfig() {
        const newConfig = {
            _id: 'ocr-config',
            service: document.getElementById('ocr-service').value,
            baidu: {
                apiKey: document.getElementById('baidu-api-key').value,
                secretKey: document.getElementById('baidu-secret-key').value,
                type: document.getElementById('baidu-type').value
            },
            tencent: {
                secretId: document.getElementById('tencent-secret-id').value,
                secretKey: document.getElementById('tencent-secret-key').value
            },
            aliyun: {
                accessKey: document.getElementById('aliyun-access-key').value,
                accessSecret: document.getElementById('aliyun-access-secret').value
            },
            llm: {
                model: document.getElementById('llm-model').value,
                apiKey: document.getElementById('llm-api-key').value,
                baseUrl: document.getElementById('llm-base-url').value
            }
        };

        // 如果配置已存在，需要保留_rev
        if (this.config._rev) {
            newConfig._rev = this.config._rev;
        }

        const result = window.ocrAPI.db.put(newConfig);
        if (result.ok) {
            this.config = newConfig;
            this.config._rev = result.rev;
            this.showMessage('配置保存成功');
        } else {
            this.showError('配置保存失败: ' + (result.message || '未知错误'));
        }
    }

    // 测试配置
    async testConfig() {
        if (!this.validateConfig()) {
            this.showError('请先完善配置信息');
            return;
        }

        this.showMessage('正在测试连接...');
        
        // 创建一个简单的测试图片（白底黑字"TEST"）
        const canvas = document.createElement('canvas');
        canvas.width = 200;
        canvas.height = 100;
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 200, 100);
        ctx.fillStyle = 'black';
        ctx.font = '30px Arial';
        ctx.fillText('TEST', 70, 60);
        
        const testImage = canvas.toDataURL();
        
        try {
            const result = await window.ocrAPI.performOCR(
                testImage,
                this.config.service,
                this.getServiceConfig()
            );

            if (result.success) {
                this.showMessage('连接测试成功');
            } else {
                this.showError('连接测试失败: ' + result.error);
            }
        } catch (error) {
            this.showError('连接测试失败: ' + error.message);
        }
    }

    // 验证配置
    validateConfig() {
        const service = this.config.service;
        const serviceConfig = this.getServiceConfig();

        switch (service) {
            case 'baidu':
                return serviceConfig.apiKey && serviceConfig.secretKey;
            case 'tencent':
                return serviceConfig.secretId && serviceConfig.secretKey;
            case 'aliyun':
                return serviceConfig.accessKey && serviceConfig.accessSecret;
            case 'llm':
                return serviceConfig.apiKey;
            default:
                return false;
        }
    }

    // 获取当前服务配置
    getServiceConfig() {
        const service = this.config.service;
        const baseConfig = this.config[service] || {};

        // 为不同服务添加特定配置
        switch (service) {
            case 'baidu':
                return {
                    apiKey: baseConfig.apiKey,
                    secretKey: baseConfig.secretKey,
                    type: baseConfig.type || 'general_basic'
                };
            case 'tencent':
                return {
                    secretId: baseConfig.secretId,
                    secretKey: baseConfig.secretKey
                };
            case 'aliyun':
                return {
                    accessKey: baseConfig.accessKey,
                    accessSecret: baseConfig.accessSecret
                };
            case 'llm':
                return {
                    model: baseConfig.model || 'openai-gpt4v',
                    apiKey: baseConfig.apiKey,
                    baseUrl: baseConfig.baseUrl
                };
            default:
                return {};
        }
    }
}

// 初始化插件
document.addEventListener('DOMContentLoaded', () => {
    new OCRPlugin();
});
