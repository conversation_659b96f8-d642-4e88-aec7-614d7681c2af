# uTools OCR 插件

一个功能强大的OCR文字识别插件，支持多种OCR服务和LLM视觉模型。

## 功能特性

### 🔍 多种OCR服务支持
- **百度OCR** - 通用文字识别、高精度文字识别
- **腾讯云OCR** - 通用印刷体识别
- **阿里云OCR** - 通用文字识别（开发中）
- **LLM视觉模型** - OpenAI GPT-4V、Claude Vision、Gemini Vision

### 📷 多种输入方式
- **屏幕截图识别** - 快捷键截图后自动识别
- **图片文件识别** - 支持拖拽或选择图片文件
- **图片匹配** - 在uTools中直接对图片进行OCR

### ⚙️ 智能配置管理
- 多服务配置切换
- 配置导入导出
- 连接测试功能
- 安全的密钥存储

### 🎨 用户友好界面
- 现代化UI设计
- 响应式布局
- 拖拽支持
- 键盘快捷键
- 实时通知

## 安装方法

1. 下载插件文件
2. 在uTools中打开插件管理
3. 选择"安装本地插件"
4. 选择插件目录进行安装

## 使用方法

### 基本使用

1. **配置OCR服务**
   - 在uTools中输入 `OCR配置` 或 `ocr config`
   - 选择要使用的OCR服务
   - 填入相应的API密钥
   - 点击"测试连接"验证配置
   - 保存配置

2. **屏幕截图识别**
   - 在uTools中输入 `OCR` 或 `文字识别`
   - 点击"屏幕截图识别"按钮
   - 选择要识别的屏幕区域
   - 等待识别完成

3. **图片文件识别**
   - 在uTools中输入 `OCR` 或 `文字识别`
   - 点击"选择图片识别"按钮选择文件
   - 或直接拖拽图片到插件窗口
   - 等待识别完成

### 快捷键

- `Ctrl/Cmd + Enter` - 快速截图识别
- `Ctrl/Cmd + C` - 复制识别结果
- `Ctrl/Cmd + S` - 保存配置（在配置页面）
- `Escape` - 返回主界面或隐藏窗口

## 配置说明

### 百度OCR配置

1. 访问 [百度AI开放平台](https://ai.baidu.com/)
2. 创建应用获取API Key和Secret Key
3. 在插件中填入相应信息

**支持的识别类型：**
- `general_basic` - 通用文字识别
- `accurate_basic` - 高精度文字识别

### 腾讯云OCR配置

1. 访问 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 开通文字识别服务
3. 获取Secret ID和Secret Key
4. 在插件中填入相应信息

### LLM视觉模型配置

#### OpenAI GPT-4V
1. 获取OpenAI API Key
2. 填入API Key
3. 可选：自定义API Base URL（用于代理服务）

#### Claude Vision
1. 获取Anthropic API Key
2. 填入API Key
3. 可选：自定义API Base URL

#### Gemini Vision
1. 获取Google AI API Key
2. 填入API Key
3. 可选：自定义API Base URL

## 开发说明

### 项目结构

```
OCR Por/
├── plugin.json          # 插件配置文件
├── index.html           # 主界面
├── preload.js          # 预加载脚本
├── assets/
│   ├── style.css       # 样式文件
│   └── logo.png        # 插件图标
└── src/
    ├── main.js         # 主逻辑
    ├── config.js       # 配置管理
    ├── ocr-services.js # OCR服务封装
    └── ui.js           # UI管理
```

### 技术栈

- **前端**: HTML5, CSS3, JavaScript ES6+
- **框架**: uTools Plugin API
- **OCR服务**: 百度OCR、腾讯云OCR、LLM APIs
- **存储**: uTools本地数据库

### 开发环境

1. 安装uTools开发者工具
2. 克隆项目到本地
3. 在uTools中加载本地插件进行调试

## 常见问题

### Q: 识别准确率不高怎么办？
A: 
1. 尝试使用高精度识别模式（百度OCR）
2. 确保图片清晰度足够
3. 尝试不同的OCR服务
4. 使用LLM视觉模型可能有更好效果

### Q: API调用失败怎么办？
A:
1. 检查网络连接
2. 验证API密钥是否正确
3. 确认API服务是否有余额
4. 检查API调用频率限制

### Q: 插件无法加载怎么办？
A:
1. 确认uTools版本兼容性
2. 检查插件文件完整性
3. 查看uTools开发者工具的错误日志

## 更新日志

### v1.0.0
- 初始版本发布
- 支持百度OCR、腾讯云OCR
- 支持OpenAI GPT-4V、Claude Vision
- 基础UI和配置管理功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个插件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- Email: [<EMAIL>]

---

**注意**: 使用本插件需要相应OCR服务的API密钥，请确保遵守各服务商的使用条款。
