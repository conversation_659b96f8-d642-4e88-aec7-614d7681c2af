<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR文字识别</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div id="app">
        <!-- 主界面 -->
        <div id="main-view" class="view">
            <div class="header">
                <h1>OCR文字识别</h1>
                <button id="config-btn" class="config-btn">⚙️ 配置</button>
            </div>
            
            <div class="action-buttons">
                <button id="screenshot-btn" class="action-btn primary">
                    📷 屏幕截图识别
                </button>
                <button id="upload-btn" class="action-btn">
                    📁 选择图片识别
                </button>
            </div>
            
            <div id="result-container" class="result-container" style="display: none;">
                <div class="result-header">
                    <h3>识别结果</h3>
                    <div class="result-actions">
                        <button id="copy-btn" class="btn-small">复制</button>
                        <button id="clear-btn" class="btn-small">清空</button>
                    </div>
                </div>
                <div id="result-text" class="result-text"></div>
            </div>
            
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>正在识别中...</p>
            </div>
        </div>
        
        <!-- 配置界面 -->
        <div id="config-view" class="view" style="display: none;">
            <div class="header">
                <button id="back-btn" class="back-btn">← 返回</button>
                <h1>OCR配置</h1>
            </div>
            
            <div class="config-content">
                <div class="config-section">
                    <h3>OCR服务选择</h3>
                    <select id="ocr-service" class="select-input">
                        <option value="baidu">百度OCR</option>
                        <option value="tencent">腾讯云OCR</option>
                        <option value="aliyun">阿里云OCR</option>
                        <option value="llm">LLM视觉模型</option>
                    </select>
                </div>
                
                <!-- 百度OCR配置 -->
                <div id="baidu-config" class="config-section">
                    <h4>百度OCR配置</h4>
                    <div class="form-group">
                        <label>API Key:</label>
                        <input type="password" id="baidu-api-key" placeholder="请输入百度OCR API Key">
                    </div>
                    <div class="form-group">
                        <label>Secret Key:</label>
                        <input type="password" id="baidu-secret-key" placeholder="请输入百度OCR Secret Key">
                    </div>
                    <div class="form-group">
                        <label>识别类型:</label>
                        <select id="baidu-type">
                            <option value="general_basic">通用文字识别</option>
                            <option value="accurate_basic">高精度文字识别</option>
                        </select>
                    </div>
                </div>
                
                <!-- 腾讯云OCR配置 -->
                <div id="tencent-config" class="config-section" style="display: none;">
                    <h4>腾讯云OCR配置</h4>
                    <div class="form-group">
                        <label>Secret ID:</label>
                        <input type="password" id="tencent-secret-id" placeholder="请输入腾讯云Secret ID">
                    </div>
                    <div class="form-group">
                        <label>Secret Key:</label>
                        <input type="password" id="tencent-secret-key" placeholder="请输入腾讯云Secret Key">
                    </div>
                </div>
                
                <!-- 阿里云OCR配置 -->
                <div id="aliyun-config" class="config-section" style="display: none;">
                    <h4>阿里云OCR配置</h4>
                    <div class="form-group">
                        <label>Access Key ID:</label>
                        <input type="password" id="aliyun-access-key" placeholder="请输入阿里云Access Key ID">
                    </div>
                    <div class="form-group">
                        <label>Access Key Secret:</label>
                        <input type="password" id="aliyun-access-secret" placeholder="请输入阿里云Access Key Secret">
                    </div>
                </div>
                
                <!-- LLM配置 -->
                <div id="llm-config" class="config-section" style="display: none;">
                    <h4>LLM视觉模型配置</h4>
                    <div class="form-group">
                        <label>模型选择:</label>
                        <select id="llm-model">
                            <option value="openai-gpt4v">OpenAI GPT-4V (gpt-4-vision-preview)</option>
                            <option value="claude-vision">Claude Vision (claude-3-opus-20240229)</option>
                            <option value="gemini-vision">Gemini Vision (gemini-1.5-flash)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>API Key:</label>
                        <input type="password" id="llm-api-key" placeholder="请输入LLM API Key">
                    </div>
                    <div class="form-group">
                        <label>API Base URL (可选):</label>
                        <input type="text" id="llm-base-url" placeholder="自定义API地址，留空使用默认">
                    </div>
                    <div class="form-group">
                        <label>最大Token数:</label>
                        <input type="number" id="llm-max-tokens" value="1000" min="100" max="4000" placeholder="1000">
                    </div>
                    <div class="form-group">
                        <label>识别提示词:</label>
                        <textarea id="llm-prompt" rows="3" placeholder="请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。">请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。</textarea>
                    </div>
                </div>
                
                <div class="config-actions">
                    <button id="save-config-btn" class="btn primary">保存配置</button>
                    <button id="test-config-btn" class="btn">测试连接</button>
                </div>
            </div>
        </div>
    </div>
    
    <input type="file" id="file-input" accept="image/*" style="display: none;">

    <!-- 引入所有模块 -->
    <script src="src/config.js"></script>
    <script src="src/ocr-services.js"></script>
    <script src="src/ui.js"></script>
    <script src="src/main.js"></script>
</body>
</html>
