# uTools OCR插件使用指南

## 快速开始

### 1. 安装插件

1. 打开uTools开发者工具
2. 选择"加载本地插件"
3. 选择插件所在文件夹
4. 插件安装完成

### 2. 配置OCR服务

首次使用需要配置OCR服务：

1. 在uTools中输入 `OCR配置` 或 `ocr config`
2. 选择要使用的OCR服务
3. 填入相应的API密钥
4. 点击"测试连接"验证配置
5. 保存配置

### 3. 开始使用

配置完成后，可以通过以下方式使用：

- 输入 `OCR` 或 `文字识别` 进入主界面
- 点击"屏幕截图识别"进行截图OCR
- 点击"选择图片识别"选择本地图片
- 直接拖拽图片到插件窗口

## 支持的OCR服务

### 百度OCR
- **获取方式**: [百度AI开放平台](https://ai.baidu.com/)
- **所需信息**: API Key, Secret Key
- **识别类型**: 通用文字识别、高精度文字识别
- **特点**: 识别速度快，准确率高

### 腾讯云OCR
- **获取方式**: [腾讯云控制台](https://console.cloud.tencent.com/)
- **所需信息**: Secret ID, Secret Key
- **识别类型**: 通用印刷体识别
- **特点**: 稳定可靠，支持多种语言

### LLM视觉模型

#### 平台配置
插件支持多个LLM平台，每个平台都有多个模型版本可选：

#### OpenAI
- **获取方式**: [OpenAI平台](https://platform.openai.com/)
- **所需信息**: API Key
- **支持模型**:
  - gpt-4-vision-preview (预览版)
  - gpt-4o (多模态旗舰)
  - gpt-4o-mini (快速经济版)
- **特点**: 理解能力强，可处理复杂图片

#### Anthropic (Claude)
- **获取方式**: [Anthropic平台](https://console.anthropic.com/)
- **所需信息**: API Key
- **支持模型**:
  - claude-3-5-sonnet-20241022 (最新最智能)
  - claude-3-opus-20240229 (最强大)
  - claude-3-sonnet-20240229 (平衡性能)
  - claude-3-haiku-20240307 (最快速)
- **特点**: 准确率高，支持多语言

#### Google (Gemini)
- **获取方式**: [Google AI Studio](https://aistudio.google.com/)
- **所需信息**: API Key
- **支持模型**:
  - gemini-1.5-flash (快速高效)
  - gemini-1.5-pro (高级推理)
  - gemini-2.0-flash-exp (实验性最新功能)
- **特点**: 免费额度大，响应速度快，支持最新功能

#### 自定义模型
- 支持输入自定义模型版本
- 例如: `gemini-2.5-flash-preview-05-20`
- 适用于新发布或实验性模型

## 快捷键

- `Ctrl/Cmd + Enter`: 快速截图识别
- `Ctrl/Cmd + C`: 复制识别结果
- `Ctrl/Cmd + S`: 保存配置（在配置页面）
- `Escape`: 返回主界面或隐藏窗口

## 功能特性

### 多种输入方式
- 屏幕截图
- 本地图片文件
- 拖拽图片
- uTools图片匹配

### 智能配置
- 多服务切换
- 配置验证
- 连接测试
- 安全存储

### 用户体验
- 现代化界面
- 实时通知
- 进度显示
- 响应式设计

## 常见问题

### Q: 如何获取API密钥？
A: 访问对应服务商的官网，注册账号后在控制台创建应用获取密钥。

### Q: 识别失败怎么办？
A: 
1. 检查网络连接
2. 验证API密钥是否正确
3. 确认API服务余额
4. 尝试其他OCR服务

### Q: 如何提高识别准确率？
A:
1. 确保图片清晰
2. 使用高精度模式
3. 尝试LLM视觉模型
4. 调整图片对比度

### Q: 支持哪些图片格式？
A: 支持常见的图片格式：PNG, JPG, JPEG, GIF, BMP等。

## 技术支持

如遇到问题，可以：
1. 查看浏览器控制台错误信息
2. 检查uTools开发者工具日志
3. 验证API服务状态
4. 重新安装插件

## 更新说明

插件会持续更新，添加新功能和修复问题。建议定期检查更新。

---

**提示**: 首次使用建议先用测试功能验证配置是否正确。
