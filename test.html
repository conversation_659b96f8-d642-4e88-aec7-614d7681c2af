<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR插件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3d8bfe;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>OCR插件功能测试</h1>
    
    <div class="test-section">
        <h2>模块加载测试</h2>
        <button class="test-button" onclick="testModuleLoading()">测试模块加载</button>
        <div id="module-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>配置管理测试</h2>
        <button class="test-button" onclick="testConfigManager()">测试配置管理</button>
        <div id="config-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>UI管理测试</h2>
        <button class="test-button" onclick="testUIManager()">测试UI管理</button>
        <div id="ui-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>OCR服务测试</h2>
        <button class="test-button" onclick="testOCRServices()">测试OCR服务</button>
        <div id="ocr-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>模型管理测试</h2>
        <div style="margin-bottom: 15px;">
            <label>选择平台:</label>
            <select id="test-platform-select" style="margin-left: 10px; padding: 5px;">
                <option value="openai">OpenAI</option>
                <option value="anthropic">Anthropic</option>
                <option value="google">Google</option>
            </select>
        </div>
        <div style="margin-bottom: 15px;">
            <label>自定义模型:</label>
            <input type="text" id="test-custom-model" placeholder="例如: gemini-2.0-flash-exp" style="margin-left: 10px; padding: 5px; width: 200px;">
        </div>
        <button class="test-button" onclick="testModelManager()">测试模型管理</button>
        <button class="test-button" onclick="testCustomModel()">测试自定义模型</button>
        <button class="test-button" onclick="testApiKeyManagement()">测试API Key管理</button>
        <div id="model-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>创建测试图片</h2>
        <button class="test-button" onclick="createTestImage()">创建测试图片</button>
        <canvas id="test-canvas" width="300" height="150" style="border: 1px solid #ccc; margin: 10px 0;"></canvas>
        <div id="image-result" class="test-result"></div>
    </div>

    <!-- 引入所有模块 -->
    <script src="src/config.js"></script>
    <script src="src/ocr-services.js"></script>
    <script src="src/ui.js"></script>
    <script src="src/model-manager.js"></script>

    <script>
        // 模拟uTools API
        window.utools = {
            db: {
                get: (id) => {
                    const data = localStorage.getItem('utools-db-' + id);
                    return data ? JSON.parse(data) : null;
                },
                put: (doc) => {
                    const id = doc._id;
                    const existingDoc = localStorage.getItem('utools-db-' + id);
                    const rev = existingDoc ? JSON.parse(existingDoc)._rev || '1' : '1';
                    doc._rev = (parseInt(rev) + 1).toString();
                    localStorage.setItem('utools-db-' + id, JSON.stringify(doc));
                    return { ok: true, id: id, rev: doc._rev };
                },
                remove: (id) => {
                    localStorage.removeItem('utools-db-' + id);
                    return { ok: true };
                }
            },
            copyText: (text) => {
                navigator.clipboard.writeText(text);
                console.log('复制文本:', text);
            },
            hideMainWindow: () => {
                console.log('隐藏主窗口');
            },
            screenCapture: (callback) => {
                console.log('模拟屏幕截图');
                // 创建一个测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 200, 100);
                ctx.fillStyle = 'black';
                ctx.font = '20px Arial';
                ctx.fillText('Hello World', 50, 50);
                callback(canvas.toDataURL());
            }
        };

        // 模拟ocrAPI
        window.ocrAPI = {
            db: window.utools.db,
            copyText: window.utools.copyText,
            hideMainWindow: window.utools.hideMainWindow,
            screenCapture: window.utools.screenCapture,
            getCurrentFeature: () => null,
            getPayload: () => null
        };

        function testModuleLoading() {
            const result = document.getElementById('module-result');
            let output = '模块加载测试结果:\n';

            try {
                // 测试ConfigManager
                if (typeof ConfigManager !== 'undefined') {
                    const configManager = new ConfigManager();
                    output += '✓ ConfigManager 加载成功\n';
                    output += `  默认服务: ${configManager.defaultConfig.service}\n`;
                } else {
                    output += '✗ ConfigManager 加载失败\n';
                }

                // 测试OCRServices
                if (typeof OCRServices !== 'undefined') {
                    const ocrServices = new OCRServices();
                    output += '✓ OCRServices 加载成功\n';
                    output += `  支持的服务: ${ocrServices.supportedServices.join(', ')}\n`;
                } else {
                    output += '✗ OCRServices 加载失败\n';
                }

                // 测试UIManager
                if (typeof UIManager !== 'undefined') {
                    const uiManager = new UIManager();
                    output += '✓ UIManager 加载成功\n';
                    output += `  当前视图: ${uiManager.currentView}\n`;
                } else {
                    output += '✗ UIManager 加载失败\n';
                }

                // 测试ModelManager
                if (typeof ModelManager !== 'undefined') {
                    const modelManager = new ModelManager();
                    output += '✓ ModelManager 加载成功\n';
                    output += `  支持的平台: ${modelManager.getSupportedPlatforms().join(', ')}\n`;
                } else {
                    output += '✗ ModelManager 加载失败\n';
                }

                result.className = 'test-result success';
            } catch (error) {
                output += `✗ 模块加载出错: ${error.message}\n`;
                result.className = 'test-result error';
            }

            result.textContent = output;
        }

        function testConfigManager() {
            const result = document.getElementById('config-result');
            let output = '配置管理测试结果:\n';
            
            try {
                const configManager = new ConfigManager();
                
                // 测试获取默认配置
                const defaultConfig = configManager.getConfig();
                output += `✓ 获取默认配置成功\n`;
                output += `  默认服务: ${defaultConfig.service}\n`;
                
                // 测试保存配置
                const testConfig = {
                    service: 'baidu',
                    baidu: {
                        apiKey: 'test-key',
                        secretKey: 'test-secret',
                        type: 'general_basic'
                    }
                };
                
                const saveResult = configManager.saveConfig(testConfig);
                if (saveResult.success) {
                    output += '✓ 配置保存成功\n';
                } else {
                    output += `✗ 配置保存失败: ${saveResult.error}\n`;
                }
                
                // 测试配置验证
                const validation = configManager.validateConfig(testConfig);
                output += `✓ 配置验证: ${validation.valid ? '通过' : '失败 - ' + validation.error}\n`;
                
                result.className = 'test-result success';
            } catch (error) {
                output += `✗ 配置管理测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }

        function testUIManager() {
            const result = document.getElementById('ui-result');
            let output = 'UI管理测试结果:\n';
            
            try {
                const uiManager = new UIManager();
                
                // 测试通知功能
                output += '✓ 创建UIManager实例成功\n';
                
                // 测试显示通知
                uiManager.showNotification('这是一个测试通知', 'success', 2000);
                output += '✓ 显示通知功能正常\n';
                
                // 测试错误显示
                uiManager.showError('这是一个测试错误');
                output += '✓ 错误显示功能正常\n';
                
                result.className = 'test-result success';
            } catch (error) {
                output += `✗ UI管理测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }

        function testOCRServices() {
            const result = document.getElementById('ocr-result');
            let output = 'OCR服务测试结果:\n';
            
            try {
                const ocrServices = new OCRServices();
                
                output += '✓ 创建OCRServices实例成功\n';
                output += `  支持的服务: ${ocrServices.supportedServices.join(', ')}\n`;
                
                // 测试加密功能
                ocrServices.sha256('test').then(hash => {
                    output += `✓ SHA256加密测试: ${hash.substring(0, 16)}...\n`;
                    document.getElementById('ocr-result').textContent = output;
                });
                
                result.className = 'test-result success';
            } catch (error) {
                output += `✗ OCR服务测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }

        function testModelManager() {
            const result = document.getElementById('model-result');
            const selectedPlatform = document.getElementById('test-platform-select').value;
            let output = `模型管理测试结果 (${selectedPlatform}):\n`;

            try {
                const modelManager = new ModelManager();

                // 测试平台信息获取
                const platformInfo = modelManager.getPlatformInfo(selectedPlatform);
                output += `✓ 平台信息获取成功\n`;
                output += `  平台名称: ${platformInfo.name}\n`;
                output += `  基础URL: ${platformInfo.baseUrl}\n`;
                output += `  默认模型数量: ${platformInfo.defaultModels.length}\n`;

                // 测试默认模型列表
                output += `✓ 默认模型列表:\n`;
                platformInfo.defaultModels.forEach((model, index) => {
                    const description = modelManager.getModelDescription(model);
                    output += `  ${index + 1}. ${model} - ${description}\n`;
                });

                // 测试模型信息获取
                const firstModel = platformInfo.defaultModels[0];
                const modelInfo = modelManager.getModelInfo(selectedPlatform, firstModel);
                output += `✓ 模型详细信息 (${firstModel}):\n`;
                output += `  API端点: ${modelInfo.apiEndpoint}\n`;
                output += `  最大Token: ${modelInfo.maxTokens}\n`;
                output += `  支持功能: ${Object.entries(modelInfo.supportedFeatures).filter(([k,v]) => v).map(([k,v]) => k).join(', ')}\n`;

                result.className = 'test-result success';
            } catch (error) {
                output += `✗ 模型管理测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }

            result.textContent = output;
        }

        function testCustomModel() {
            const result = document.getElementById('model-result');
            const selectedPlatform = document.getElementById('test-platform-select').value;
            const customModel = document.getElementById('test-custom-model').value;
            let output = `自定义模型测试结果:\n`;

            if (!customModel) {
                output += '✗ 请输入自定义模型名称\n';
                result.className = 'test-result error';
                result.textContent = output;
                return;
            }

            try {
                const modelManager = new ModelManager();

                // 测试自定义模型验证
                const validation = modelManager.validateCustomModel(selectedPlatform, customModel);
                output += `✓ 模型名称验证: ${validation.valid ? '通过' : '失败 - ' + validation.error}\n`;

                if (validation.valid) {
                    // 测试模型信息获取
                    const modelInfo = modelManager.getModelInfo(selectedPlatform, customModel);
                    output += `✓ 自定义模型信息:\n`;
                    output += `  平台: ${modelInfo.platform}\n`;
                    output += `  模型ID: ${modelInfo.modelId}\n`;
                    output += `  描述: ${modelInfo.description}\n`;
                    output += `  API端点: ${modelInfo.apiEndpoint}\n`;
                    output += `  最大Token: ${modelInfo.maxTokens}\n`;

                    // 测试配置生成
                    const testConfig = {
                        service: 'llm',
                        llm: {
                            platform: selectedPlatform,
                            model: customModel,
                            useCustomModel: true,
                            customModel: customModel,
                            apiKey: 'test-api-key',
                            baseUrl: '',
                            maxTokens: modelInfo.maxTokens,
                            prompt: '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。'
                        }
                    };

                    const configManager = new ConfigManager();
                    const serviceConfig = configManager.getServiceConfig(testConfig, 'llm');
                    output += `✓ 配置生成成功:\n`;
                    output += `  最终使用模型: ${serviceConfig.model}\n`;
                    output += `  使用自定义模型: ${serviceConfig.useCustomModel}\n`;
                }

                result.className = 'test-result success';
            } catch (error) {
                output += `✗ 自定义模型测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }

            result.textContent = output;
        }

        function testApiKeyManagement() {
            const result = document.getElementById('model-result');
            let output = 'API Key管理测试结果:\n';

            try {
                const configManager = new ConfigManager();

                // 测试多平台API Key存储
                const testConfig = {
                    service: 'llm',
                    llm: {
                        platform: 'openai',
                        apiKeys: {
                            openai: 'sk-test-openai-key',
                            anthropic: 'sk-ant-test-key',
                            google: 'AIza-test-google-key',
                            custom: 'custom-test-key'
                        }
                    }
                };

                // 测试配置保存
                const saveResult = configManager.saveConfig(testConfig);
                output += `✓ 多平台API Key配置保存: ${saveResult.success ? '成功' : '失败'}\n`;

                // 测试不同平台的API Key获取
                const platforms = ['openai', 'anthropic', 'google'];
                platforms.forEach(platform => {
                    testConfig.llm.platform = platform;
                    const serviceConfig = configManager.getServiceConfig(testConfig, 'llm');
                    const expectedKey = testConfig.llm.apiKeys[platform];
                    const actualKey = serviceConfig.apiKey;

                    output += `✓ ${platform}平台API Key: ${actualKey === expectedKey ? '正确' : '错误'}\n`;
                    output += `  期望: ${expectedKey}\n`;
                    output += `  实际: ${actualKey}\n`;
                });

                // 测试API Key独立性
                output += `✓ API Key独立存储测试:\n`;
                output += `  OpenAI: ${testConfig.llm.apiKeys.openai}\n`;
                output += `  Anthropic: ${testConfig.llm.apiKeys.anthropic}\n`;
                output += `  Google: ${testConfig.llm.apiKeys.google}\n`;

                result.className = 'test-result success';
            } catch (error) {
                output += `✗ API Key管理测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }

            result.textContent = output;
        }

        function createTestImage() {
            const canvas = document.getElementById('test-canvas');
            const ctx = canvas.getContext('2d');
            const result = document.getElementById('image-result');

            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制测试文字
            ctx.fillStyle = 'black';
            ctx.font = '24px Arial';
            ctx.fillText('OCR测试文字', 50, 50);
            ctx.font = '16px Arial';
            ctx.fillText('Test Text 123', 50, 80);
            ctx.fillText('测试 Test 2024', 50, 110);

            // 添加边框
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.strokeRect(0, 0, canvas.width, canvas.height);

            const imageData = canvas.toDataURL();
            result.textContent = `测试图片已生成\nBase64长度: ${imageData.length} 字符\n前50个字符: ${imageData.substring(0, 50)}...`;
            result.className = 'test-result success';
        }

        // 页面加载完成后自动运行模块加载测试
        window.addEventListener('load', () => {
            setTimeout(testModuleLoading, 500);
        });
    </script>
</body>
</html>
