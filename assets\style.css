* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
}

.view {
    height: 100vh;
    padding: 20px;
    overflow-y: auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
    color: white;
    font-size: 24px;
    font-weight: 600;
}

.config-btn, .back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.config-btn:hover, .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.action-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.action-btn {
    flex: 1;
    padding: 20px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-btn.primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.result-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-header h3 {
    color: #333;
    font-size: 18px;
}

.result-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    background: #f0f0f0;
    color: #666;
    transition: all 0.3s ease;
}

.btn-small:hover {
    background: #e0e0e0;
}

.result-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    min-height: 100px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.config-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.config-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.config-section:last-child {
    border-bottom: none;
}

.config-section h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 15px;
}

.config-section h4 {
    color: #555;
    font-size: 16px;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-weight: 500;
}

.select-input, input[type="text"], input[type="password"], input[type="number"], textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.select-input:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="number"]:focus, textarea:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
}

textarea {
    resize: vertical;
    min-height: 60px;
}

/* 模型选择相关样式 */
.model-selection {
    display: flex;
    align-items: center;
}

.model-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
}

.model-info h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.model-info .model-detail {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-size: 13px;
}

.model-info .model-detail .label {
    font-weight: 500;
    color: #6c757d;
}

.model-info .model-detail .value {
    color: #495057;
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.loading-models {
    color: #6c757d;
    font-style: italic;
}

.error-models {
    color: #dc3545;
    font-size: 12px;
}

/* 复选框样式 */
input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

label input[type="checkbox"] {
    margin-right: 8px;
}

/* API Key输入框和切换按钮样式 */
.input-with-toggle {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-toggle input {
    flex: 1;
    padding-right: 45px; /* 为按钮留出空间 */
}

.toggle-password {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.toggle-password:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.toggle-password:active {
    background-color: rgba(0, 0, 0, 0.1);
}

.eye-icon {
    display: inline-block;
    transition: opacity 0.2s ease;
}

.toggle-password.hidden .eye-icon {
    opacity: 0.6;
}

.toggle-password.visible .eye-icon {
    opacity: 1;
}

/* 为不同状态提供不同的图标 */
.toggle-password.hidden .eye-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: #666;
    transform: rotate(45deg);
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -1px;
}

.config-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    color: #333;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn.primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn.primary:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
}

/* 置信度显示 */
.confidence-info {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.confidence-info.high {
    background: #d4edda;
    color: #155724;
}

.confidence-info.medium {
    background: #fff3cd;
    color: #856404;
}

.confidence-info.low {
    background: #f8d7da;
    color: #721c24;
}

/* 拖拽区域样式 */
.drop-zone-content {
    text-align: center;
}

.drop-zone-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.drop-zone-text {
    font-size: 18px;
    font-weight: 500;
}

/* 进度条样式 */
.progress-container {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-size: 14px;
    color: #666;
}

/* 移动端布局 */
.mobile-layout .action-buttons {
    flex-direction: column;
}

.mobile-layout .config-actions {
    flex-direction: column;
}

.mobile-layout .result-actions {
    flex-direction: column;
    gap: 5px;
}

.mobile-layout .header h1 {
    font-size: 20px;
}

.mobile-layout .action-btn {
    padding: 15px;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .action-buttons {
        flex-direction: column;
    }

    .config-actions {
        flex-direction: column;
    }

    .result-actions {
        flex-direction: column;
        gap: 5px;
    }

    .header h1 {
        font-size: 20px;
    }

    .action-btn {
        padding: 15px;
        font-size: 14px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
