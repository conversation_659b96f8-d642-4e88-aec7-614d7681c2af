const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// OCR服务接口
const OCRServices = {
    // 百度OCR
    async baiduOCR(imageBase64, config) {
        const https = require('https');
        const querystring = require('querystring');
        
        try {
            // 获取access_token
            const tokenUrl = 'https://aip.baidubce.com/oauth/2.0/token';
            const tokenParams = querystring.stringify({
                grant_type: 'client_credentials',
                client_id: config.apiKey,
                client_secret: config.secretKey
            });
            
            const accessToken = await new Promise((resolve, reject) => {
                const req = https.request(`${tokenUrl}?${tokenParams}`, { method: 'POST' }, (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        try {
                            const result = JSON.parse(data);
                            if (result.access_token) {
                                resolve(result.access_token);
                            } else {
                                reject(new Error(result.error_description || '获取access_token失败'));
                            }
                        } catch (e) {
                            reject(e);
                        }
                    });
                });
                req.on('error', reject);
                req.end();
            });
            
            // OCR识别
            const ocrUrl = `https://aip.baidubce.com/rest/2.0/ocr/v1/${config.type}`;
            const ocrParams = querystring.stringify({
                image: imageBase64.replace(/^data:image\/[a-z]+;base64,/, ''),
                access_token: accessToken
            });
            
            return new Promise((resolve, reject) => {
                const req = https.request(ocrUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Content-Length': Buffer.byteLength(ocrParams)
                    }
                }, (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        try {
                            const result = JSON.parse(data);
                            if (result.words_result) {
                                const text = result.words_result.map(item => item.words).join('\n');
                                resolve({ success: true, text });
                            } else {
                                reject(new Error(result.error_msg || '识别失败'));
                            }
                        } catch (e) {
                            reject(e);
                        }
                    });
                });
                req.on('error', reject);
                req.write(ocrParams);
                req.end();
            });
        } catch (error) {
            return { success: false, error: error.message };
        }
    },
    
    // 腾讯云OCR
    async tencentOCR(imageBase64, config) {
        const https = require('https');
        
        try {
            // 腾讯云API签名算法
            const timestamp = Math.floor(Date.now() / 1000);
            const date = new Date(timestamp * 1000).toISOString().substr(0, 10);
            
            const payload = JSON.stringify({
                ImageBase64: imageBase64.replace(/^data:image\/[a-z]+;base64,/, '')
            });
            
            // 构建签名
            const algorithm = 'TC3-HMAC-SHA256';
            const service = 'ocr';
            const version = '2018-11-19';
            const action = 'GeneralBasicOCR';
            const region = 'ap-beijing';
            const host = 'ocr.tencentcloudapi.com';
            
            const canonicalRequest = [
                'POST',
                '/',
                '',
                `content-type:application/json; charset=utf-8\nhost:${host}\n`,
                'content-type;host',
                crypto.createHash('sha256').update(payload).digest('hex')
            ].join('\n');
            
            const stringToSign = [
                algorithm,
                timestamp,
                `${date}/${service}/tc3_request`,
                crypto.createHash('sha256').update(canonicalRequest).digest('hex')
            ].join('\n');
            
            const secretDate = crypto.createHmac('sha256', `TC3${config.secretKey}`).update(date).digest();
            const secretService = crypto.createHmac('sha256', secretDate).update(service).digest();
            const secretSigning = crypto.createHmac('sha256', secretService).update('tc3_request').digest();
            const signature = crypto.createHmac('sha256', secretSigning).update(stringToSign).digest('hex');
            
            const authorization = `${algorithm} Credential=${config.secretId}/${date}/${service}/tc3_request, SignedHeaders=content-type;host, Signature=${signature}`;
            
            return new Promise((resolve, reject) => {
                const req = https.request(`https://${host}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': authorization,
                        'Content-Type': 'application/json; charset=utf-8',
                        'Host': host,
                        'X-TC-Action': action,
                        'X-TC-Timestamp': timestamp,
                        'X-TC-Version': version,
                        'X-TC-Region': region
                    }
                }, (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        try {
                            const result = JSON.parse(data);
                            if (result.Response && result.Response.TextDetections) {
                                const text = result.Response.TextDetections.map(item => item.DetectedText).join('\n');
                                resolve({ success: true, text });
                            } else {
                                reject(new Error(result.Response?.Error?.Message || '识别失败'));
                            }
                        } catch (e) {
                            reject(e);
                        }
                    });
                });
                req.on('error', reject);
                req.write(payload);
                req.end();
            });
        } catch (error) {
            return { success: false, error: error.message };
        }
    },
    
    // LLM视觉模型OCR
    async llmOCR(imageBase64, config) {
        const https = require('https');
        
        try {
            let apiUrl, headers, payload;
            
            if (config.model === 'openai-gpt4v') {
                apiUrl = config.baseUrl || 'https://api.openai.com';
                headers = {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json'
                };
                payload = JSON.stringify({
                    model: 'gpt-4-vision-preview',
                    messages: [{
                        role: 'user',
                        content: [
                            { type: 'text', text: '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。' },
                            { type: 'image_url', image_url: { url: imageBase64 } }
                        ]
                    }],
                    max_tokens: 1000
                });
            } else if (config.model === 'claude-vision') {
                apiUrl = config.baseUrl || 'https://api.anthropic.com';
                headers = {
                    'x-api-key': config.apiKey,
                    'Content-Type': 'application/json',
                    'anthropic-version': '2023-06-01'
                };
                payload = JSON.stringify({
                    model: 'claude-3-opus-20240229',
                    max_tokens: 1000,
                    messages: [{
                        role: 'user',
                        content: [
                            { type: 'text', text: '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。' },
                            { type: 'image', source: { type: 'base64', media_type: 'image/png', data: imageBase64.replace(/^data:image\/[a-z]+;base64,/, '') } }
                        ]
                    }]
                });
            }
            
            return new Promise((resolve, reject) => {
                const url = new URL(apiUrl + (config.model === 'openai-gpt4v' ? '/v1/chat/completions' : '/v1/messages'));
                const req = https.request(url, {
                    method: 'POST',
                    headers
                }, (res) => {
                    let data = '';
                    res.on('data', chunk => data += chunk);
                    res.on('end', () => {
                        try {
                            const result = JSON.parse(data);
                            let text = '';
                            
                            if (config.model === 'openai-gpt4v' && result.choices?.[0]?.message?.content) {
                                text = result.choices[0].message.content;
                            } else if (config.model === 'claude-vision' && result.content?.[0]?.text) {
                                text = result.content[0].text;
                            }
                            
                            if (text) {
                                resolve({ success: true, text });
                            } else {
                                reject(new Error('识别失败'));
                            }
                        } catch (e) {
                            reject(e);
                        }
                    });
                });
                req.on('error', reject);
                req.write(payload);
                req.end();
            });
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
};

// 暴露API给渲染进程
window.ocrAPI = {
    // 屏幕截图
    screenCapture: (callback) => {
        utools.screenCapture(callback);
    },
    
    // OCR识别
    async performOCR(imageBase64, service, config) {
        switch (service) {
            case 'baidu':
                return await OCRServices.baiduOCR(imageBase64, config);
            case 'tencent':
                return await OCRServices.tencentOCR(imageBase64, config);
            case 'llm':
                return await OCRServices.llmOCR(imageBase64, config);
            default:
                return { success: false, error: '不支持的OCR服务' };
        }
    },
    
    // 数据库操作
    db: {
        get: (id) => utools.db.get(id),
        put: (doc) => utools.db.put(doc),
        remove: (id) => utools.db.remove(id)
    },
    
    // 复制到剪贴板
    copyText: (text) => {
        utools.copyText(text);
    },
    
    // 隐藏窗口
    hideMainWindow: () => {
        utools.hideMainWindow();
    },
    
    // 获取当前功能代码
    getCurrentFeature: () => {
        return utools.getCurrentBrowserWindow().webContents.getURL().includes('code=') 
            ? new URLSearchParams(window.location.search).get('code')
            : null;
    }
};
