// uTools插件预加载脚本

// 暴露API给渲染进程
window.ocrAPI = {
    // 屏幕截图
    screenCapture: (callback) => {
        utools.screenCapture(callback);
    },

    // 数据库操作
    db: {
        get: (id) => utools.db.get(id),
        put: (doc) => utools.db.put(doc),
        remove: (id) => utools.db.remove(id)
    },

    // 复制到剪贴板
    copyText: (text) => {
        utools.copyText(text);
    },

    // 隐藏窗口
    hideMainWindow: () => {
        utools.hideMainWindow();
    },

    // 获取当前功能代码
    getCurrentFeature: () => {
        // 从uTools获取当前功能代码
        const feature = utools.getFeatures().find(f => f.code);
        return feature ? feature.code : null;
    },

    // 获取传入的数据
    getPayload: () => {
        return utools.getPayload();
    }
};
