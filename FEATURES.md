# 功能特性详解

## 🔑 API Key管理功能

### 多平台独立存储
- **OpenAI**: 存储OpenAI API Key
- **Anthropic**: 存储Claude API Key  
- **Google**: 存储Gemini API Key
- **自定义**: 支持其他平台API Key

### 平台切换自动加载
- 切换平台时自动加载对应的API Key
- 避免显示错误平台的密钥
- 支持空值状态，不会相互干扰

### 安全显示控制
- **默认隐藏**: API Key默认以密码形式显示
- **一键切换**: 点击眼睛图标切换显示/隐藏
- **视觉反馈**: 图标状态反映当前显示模式
- **安全提示**: 鼠标悬停显示操作提示

## 🤖 动态模型选择

### API获取模型列表
```javascript
// OpenAI
GET https://api.openai.com/v1/models
Authorization: Bearer YOUR_API_KEY

// Google Gemini  
GET https://generativelanguage.googleapis.com/v1beta/models?key=YOUR_API_KEY

// Anthropic (使用预设列表)
claude-3-5-sonnet-20241022
claude-3-opus-20240229
claude-3-sonnet-20240229
claude-3-haiku-20240307
```

### 自定义模型版本
支持手动输入最新或实验性模型：
- `gemini-2.0-flash-exp`
- `gemini-2.5-flash-preview-05-20`
- `gpt-4-vision-preview`
- `claude-3-5-sonnet-20241022`

### 模型信息展示
- **平台信息**: 显示所属平台
- **模型ID**: 完整的模型标识符
- **API端点**: 具体的调用地址
- **最大Token**: 推荐的Token限制
- **功能支持**: 支持的特性列表

## 🔄 智能配置管理

### 平台切换流程
1. 用户选择新平台
2. 自动加载该平台的API Key
3. 更新输入框placeholder提示
4. 获取该平台的模型列表
5. 显示默认或已选模型

### 配置保存策略
```json
{
  "llm": {
    "platform": "openai",
    "model": "gpt-4-vision-preview",
    "useCustomModel": false,
    "customModel": "",
    "apiKeys": {
      "openai": "sk-xxx",
      "anthropic": "sk-ant-xxx", 
      "google": "AIza-xxx",
      "custom": ""
    },
    "baseUrl": "",
    "maxTokens": 4096,
    "prompt": "请识别图片中的所有文字内容..."
  }
}
```

### 缓存机制
- **模型列表缓存**: 5分钟有效期
- **减少API调用**: 避免频繁请求
- **手动刷新**: 支持强制更新列表
- **错误降级**: API失败时使用默认列表

## 🎨 用户界面优化

### API Key输入框
```html
<div class="input-with-toggle">
    <input type="password" id="llm-api-key" placeholder="请输入OpenAI API Key">
    <button type="button" class="toggle-password" title="显示/隐藏API Key">
        <span class="eye-icon">👁️</span>
    </button>
</div>
```

### 样式特性
- **响应式设计**: 适配不同屏幕尺寸
- **视觉反馈**: 悬停和点击效果
- **状态指示**: 清晰的显示/隐藏状态
- **无障碍支持**: 键盘导航和屏幕阅读器

### 交互体验
- **即时反馈**: 操作立即生效
- **状态保持**: 记住用户选择
- **错误提示**: 友好的错误信息
- **加载状态**: 清晰的加载指示

## 🔧 技术实现

### 模型管理器架构
```javascript
class ModelManager {
    constructor() {
        this.platforms = {
            openai: { name: 'OpenAI', baseUrl: '...', modelsEndpoint: '...' },
            anthropic: { name: 'Anthropic', baseUrl: '...', defaultModels: [...] },
            google: { name: 'Google', baseUrl: '...', modelsEndpoint: '...' }
        };
        this.modelCache = new Map();
        this.lastFetchTime = new Map();
    }
}
```

### 事件处理机制
- **平台切换**: `handlePlatformChange(platform)`
- **模型选择**: `handleModelChange(modelId)`
- **API Key切换**: `toggleApiKeyVisibility()`
- **刷新模型**: `refreshModelList()`

### 数据验证
- **模型名称格式**: 平台特定的命名规则
- **API Key格式**: 基本的格式验证
- **配置完整性**: 必填字段检查
- **连接测试**: 实际API调用验证

## 📱 使用场景

### 开发者场景
- 测试不同模型的效果
- 切换API提供商
- 使用最新的实验性模型

### 企业场景  
- 多团队使用不同平台
- 成本控制和配额管理
- 合规性要求

### 个人用户场景
- 比较不同模型性能
- 利用免费额度
- 体验最新功能

## 🔒 安全考虑

### API Key保护
- 默认隐藏显示
- 本地安全存储
- 不在日志中记录
- 传输加密保护

### 数据隐私
- 配置本地存储
- 不上传到服务器
- 用户完全控制
- 支持数据清除

---

这些功能的实现大大提升了插件的易用性、安全性和灵活性，为用户提供了更好的OCR体验。
