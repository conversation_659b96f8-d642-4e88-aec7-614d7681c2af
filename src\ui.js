// UI管理模块
class UIManager {
    constructor() {
        this.currentView = 'main';
        this.isLoading = false;
        this.notifications = [];
    }

    // 初始化UI
    init() {
        this.bindGlobalEvents();
        this.setupKeyboardShortcuts();
    }

    // 绑定全局事件
    bindGlobalEvents() {
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.adjustLayout();
        });

        // 拖拽文件支持
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.showDropZone();
        });

        document.addEventListener('dragleave', (e) => {
            if (!e.relatedTarget) {
                this.hideDropZone();
            }
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            this.hideDropZone();
            this.handleFileDrop(e);
        });
    }

    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter: 快速截图
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                if (this.currentView === 'main') {
                    document.getElementById('screenshot-btn').click();
                }
            }

            // Escape: 返回主界面或隐藏窗口
            if (e.key === 'Escape') {
                if (this.currentView === 'config') {
                    this.showMainView();
                } else {
                    window.ocrAPI?.hideMainWindow?.();
                }
            }

            // Ctrl/Cmd + C: 复制结果
            if ((e.ctrlKey || e.metaKey) && e.key === 'c' && this.currentView === 'main') {
                const resultText = document.getElementById('result-text').textContent;
                if (resultText) {
                    e.preventDefault();
                    window.ocrAPI?.copyText?.(resultText);
                    this.showNotification('已复制到剪贴板');
                }
            }

            // Ctrl/Cmd + S: 保存配置
            if ((e.ctrlKey || e.metaKey) && e.key === 's' && this.currentView === 'config') {
                e.preventDefault();
                document.getElementById('save-config-btn').click();
            }
        });
    }

    // 显示主视图
    showMainView() {
        document.getElementById('config-view').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.currentView = 'main';
        this.adjustLayout();
    }

    // 显示配置视图
    showConfigView() {
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('config-view').style.display = 'block';
        this.currentView = 'config';
        this.adjustLayout();
    }

    // 显示加载状态
    showLoading(message = '正在处理...') {
        const loading = document.getElementById('loading');
        loading.querySelector('p').textContent = message;
        loading.style.display = 'flex';
        this.isLoading = true;
        
        // 禁用操作按钮
        this.setButtonsEnabled(false);
    }

    // 隐藏加载状态
    hideLoading() {
        document.getElementById('loading').style.display = 'none';
        this.isLoading = false;
        
        // 启用操作按钮
        this.setButtonsEnabled(true);
    }

    // 设置按钮启用状态
    setButtonsEnabled(enabled) {
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.disabled = !enabled;
        });
    }

    // 显示结果
    showResult(text, confidence = null, details = null) {
        const resultContainer = document.getElementById('result-container');
        const resultText = document.getElementById('result-text');
        
        resultText.textContent = text;
        resultContainer.style.display = 'block';
        
        // 如果有置信度信息，显示
        if (confidence !== null) {
            this.showConfidence(confidence);
        }
        
        // 滚动到结果区域
        resultContainer.scrollIntoView({ behavior: 'smooth' });
        
        // 自动复制（如果配置了）
        const config = window.configManager?.getConfig();
        if (config?.ui?.copyAfterOCR) {
            window.ocrAPI?.copyText?.(text);
            this.showNotification('已自动复制到剪贴板');
        }
    }

    // 显示置信度
    showConfidence(confidence) {
        let confidenceEl = document.getElementById('confidence-info');
        if (!confidenceEl) {
            confidenceEl = document.createElement('div');
            confidenceEl.id = 'confidence-info';
            confidenceEl.className = 'confidence-info';
            document.querySelector('.result-header').appendChild(confidenceEl);
        }
        
        const percentage = Math.round(confidence * 100);
        confidenceEl.textContent = `置信度: ${percentage}%`;
        confidenceEl.className = `confidence-info ${percentage >= 80 ? 'high' : percentage >= 60 ? 'medium' : 'low'}`;
    }

    // 清空结果
    clearResult() {
        document.getElementById('result-container').style.display = 'none';
        document.getElementById('result-text').textContent = '';
        
        const confidenceEl = document.getElementById('confidence-info');
        if (confidenceEl) {
            confidenceEl.remove();
        }
    }

    // 显示通知
    showNotification(message, type = 'success', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // 样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        // 根据类型设置颜色
        switch (type) {
            case 'success':
                notification.style.background = '#4CAF50';
                break;
            case 'error':
                notification.style.background = '#f44336';
                break;
            case 'warning':
                notification.style.background = '#ff9800';
                break;
            case 'info':
                notification.style.background = '#2196F3';
                break;
        }
        
        document.body.appendChild(notification);
        
        // 动画显示
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, duration);
        
        this.notifications.push(notification);
    }

    // 显示错误
    showError(message) {
        this.showNotification(message, 'error', 5000);
    }

    // 显示拖拽区域
    showDropZone() {
        let dropZone = document.getElementById('drop-zone');
        if (!dropZone) {
            dropZone = document.createElement('div');
            dropZone.id = 'drop-zone';
            dropZone.innerHTML = `
                <div class="drop-zone-content">
                    <div class="drop-zone-icon">📁</div>
                    <div class="drop-zone-text">拖拽图片到此处进行OCR识别</div>
                </div>
            `;
            dropZone.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(79, 172, 254, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 999;
                color: white;
                font-size: 18px;
                text-align: center;
            `;
            document.body.appendChild(dropZone);
        }
        dropZone.style.display = 'flex';
    }

    // 隐藏拖拽区域
    hideDropZone() {
        const dropZone = document.getElementById('drop-zone');
        if (dropZone) {
            dropZone.style.display = 'none';
        }
    }

    // 处理文件拖拽
    handleFileDrop(event) {
        const files = Array.from(event.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        
        if (imageFiles.length === 0) {
            this.showError('请拖拽图片文件');
            return;
        }
        
        // 处理第一个图片文件
        const file = imageFiles[0];
        const reader = new FileReader();
        reader.onload = (e) => {
            if (window.ocrPlugin) {
                window.ocrPlugin.performOCR(e.target.result);
            }
        };
        reader.readAsDataURL(file);
    }

    // 调整布局
    adjustLayout() {
        // 根据窗口大小调整布局
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        if (width < 600) {
            document.body.classList.add('mobile-layout');
        } else {
            document.body.classList.remove('mobile-layout');
        }
    }

    // 切换配置区域
    switchConfigSection(service) {
        const sections = ['baidu-config', 'tencent-config', 'aliyun-config', 'llm-config'];
        sections.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });

        const sectionMap = {
            'baidu': 'baidu-config',
            'tencent': 'tencent-config',
            'aliyun': 'aliyun-config',
            'llm': 'llm-config'
        };

        const targetSection = sectionMap[service];
        if (targetSection) {
            const element = document.getElementById(targetSection);
            if (element) {
                element.style.display = 'block';
            }
        }
    }

    // 更新进度
    updateProgress(percentage, message) {
        let progressEl = document.getElementById('progress-bar');
        if (!progressEl) {
            progressEl = document.createElement('div');
            progressEl.id = 'progress-bar';
            progressEl.innerHTML = `
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text"></div>
                </div>
            `;
            progressEl.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.95);
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            `;
            document.body.appendChild(progressEl);
        }
        
        const fill = progressEl.querySelector('.progress-fill');
        const text = progressEl.querySelector('.progress-text');
        
        fill.style.width = `${percentage}%`;
        text.textContent = message || `${percentage}%`;
        
        if (percentage >= 100) {
            setTimeout(() => {
                if (progressEl.parentNode) {
                    document.body.removeChild(progressEl);
                }
            }, 1000);
        }
    }

    // 清理所有通知
    clearNotifications() {
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        });
        this.notifications = [];
    }
}

// 导出UI管理器
window.UIManager = UIManager;
